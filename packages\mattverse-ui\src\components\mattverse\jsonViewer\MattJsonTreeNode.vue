<template>
  <div class="json-tree-node" :class="{ 'search-match': isMatch }">
    <div
      class="node-content"
      :class="{
        'cursor-pointer': true,
        'selected-node': isSelected,
      }"
      @click.stop="handleNodeClick"
    >
      <span v-if="isExpandable" class="toggle-icon mr-1">
        <ChevronRight v-if="!isExpanded" class="h-3 w-3 transition-transform" />
        <ChevronDown v-else class="h-3 w-3 transition-transform" />
      </span>

      <span v-if="keyName" class="key-name">
        <span :class="{ 'search-highlight': keyNameMatch }">{{ keyName }}</span>
        :
      </span>

      <span class="value" :class="getValueClass(data)">
        <template v-if="isExpandable">
          {{ getCollapsedPreview(data) }}
        </template>
        <template v-else>
          {{ getFormattedValue(data) }}
        </template>
      </span>
    </div>

    <div v-if="isExpanded" class="children ml-4">
      <template v-if="isArray">
        <div v-for="(item, index) in data" :key="index" class="array-item">
          <MattJsonTreeNode
            :data="item"
            :level="level + 1"
            :path="`${path}[${index}]`"
            :key-name="index.toString()"
            :expanded-paths="expandedPaths"
            :search-text="searchText"
            :selected-path="selectedPath"
            @toggle="handleToggle"
            @select="handleSelect"
          />
        </div>
      </template>

      <template v-else-if="isObject">
        <div v-for="key in sortedKeys" :key="key" class="object-property">
          <MattJsonTreeNode
            :data="data[key]"
            :level="level + 1"
            :path="`${path}.${key}`"
            :key-name="key"
            :expanded-paths="expandedPaths"
            :search-text="searchText"
            :selected-path="selectedPath"
            @toggle="handleToggle"
            @select="handleSelect"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ChevronRight, ChevronDown } from 'lucide-vue-next'

interface Props {
  data: any
  level: number
  path: string
  keyName?: string
  expandedPaths: Set<string>
  searchText: string
  selectedPath: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'toggle', path: string): void
  (e: 'select', path: string, data: any): void
}>()

// 计算属性
const isObject = computed(
  () => props.data !== null && typeof props.data === 'object' && !Array.isArray(props.data)
)
const isArray = computed(() => Array.isArray(props.data))
const isExpandable = computed(() => isObject.value || isArray.value)
const isExpanded = computed(() => props.expandedPaths.has(props.path))
const isSelected = computed(() => props.selectedPath === props.path)

// 对象的键按字母顺序排序
const sortedKeys = computed(() => {
  if (!isObject.value) return []
  return Object.keys(props.data).sort()
})

// 搜索匹配
const keyNameMatch = computed(() => {
  if (!props.searchText || !props.keyName) return false
  return props.keyName.toLowerCase().includes(props.searchText.toLowerCase())
})

const isMatch = computed(() => {
  if (!props.searchText) return false

  // 检查键名是否匹配
  if (keyNameMatch.value) return true

  // 检查值是否匹配（对于简单类型）
  if (!isExpandable.value) {
    const valueStr = String(props.data)
    return valueStr.toLowerCase().includes(props.searchText.toLowerCase())
  }

  return false
})

// 处理节点点击
const handleNodeClick = (event: MouseEvent) => {
  event.stopPropagation()

  // 选择当前节点
  emit('select', props.path, props.data)

  // 如果是可展开的节点，切换展开状态
  if (isExpandable.value) {
    emit('toggle', props.path)
  }
}

// 处理子节点的 toggle 事件
const handleToggle = (path: string) => {
  emit('toggle', path)
}

// 处理子节点的 select 事件
const handleSelect = (path: string, data: any) => {
  emit('select', path, data)
}

// 获取值的类型样式
const getValueClass = (value: any) => {
  if (value === null) return 'null-value'
  if (typeof value === 'string') return 'string-value'
  if (typeof value === 'number') return 'number-value'
  if (typeof value === 'boolean') return 'boolean-value'
  if (Array.isArray(value)) return 'array-value'
  if (typeof value === 'object') return 'object-value'
  return ''
}

// 获取格式化的值
const getFormattedValue = (value: any) => {
  if (value === null) return 'null'
  if (value === undefined) return 'undefined'
  if (typeof value === 'string') return `"${value}"`
  return String(value)
}

// 获取折叠状态下的预览
const getCollapsedPreview = (value: any) => {
  if (Array.isArray(value)) {
    return `Array(${value.length})`
  }
  if (typeof value === 'object' && value !== null) {
    const keys = Object.keys(value)
    return `Object{${keys.length}}`
  }
  return ''
}
</script>

<style scoped>
.json-tree-node {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.node-content {
  display: flex;
  align-items: center;
  border-radius: 0.25rem;
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.node-content:hover {
  background-color: rgb(243 244 246);
}

.selected-node {
  background-color: rgb(239 246 255);
}

.selected-node:hover {
  background-color: rgb(219 234 254);
}

.key-name {
  color: rgb(37 99 235);
  margin-right: 0.25rem;
}

.string-value {
  color: rgb(22 163 74);
}

.number-value {
  color: rgb(234 88 12);
}

.boolean-value {
  color: rgb(147 51 234);
}

.null-value {
  color: rgb(107 114 128);
}

.array-value,
.object-value {
  color: rgb(75 85 99);
}

.search-match {
  background-color: rgb(254 249 195);
}

.search-highlight {
  background-color: rgb(254 240 138);
  padding-left: 0.125rem;
  padding-right: 0.125rem;
  border-radius: 0.25rem;
}

.toggle-icon {
  color: rgb(107 114 128);
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  .node-content:hover {
    background-color: rgba(31 41 55 / 0.5);
  }

  .selected-node {
    background-color: rgba(30 58 138 / 0.2);
  }

  .selected-node:hover {
    background-color: rgba(30 64 175 / 0.3);
  }

  .key-name {
    color: rgb(96 165 250);
  }

  .string-value {
    color: rgb(74 222 128);
  }

  .number-value {
    color: rgb(251 146 60);
  }

  .boolean-value {
    color: rgb(196 181 253);
  }

  .null-value {
    color: rgb(156 163 175);
  }

  .array-value,
  .object-value {
    color: rgb(209 213 219);
  }

  .search-match {
    background-color: rgba(133 77 14 / 0.3);
  }

  .search-highlight {
    background-color: rgba(133 77 14 / 0.5);
  }

  .toggle-icon {
    color: rgb(156 163 175);
  }
}
</style>

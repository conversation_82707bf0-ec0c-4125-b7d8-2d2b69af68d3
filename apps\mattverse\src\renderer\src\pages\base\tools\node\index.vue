<template>
  <div class="page-layout">
    <!-- 页面标题区域 -->
    <div class="search-section">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-primary">节点工具模块</h1>
          <p class="text-muted-foreground mt-1">JSON 数据查看器和节点配置工具</p>
        </div>
        <div class="flex items-center gap-2">
          <Button variant="outline" @click="loadSampleData">
            <FileText class="h-4 w-4 mr-2" />
            加载示例数据
          </Button>
          <Button variant="outline" @click="clearData">
            <Trash2 class="h-4 w-4 mr-2" />
            清空数据
          </Button>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-section">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
        <!-- 左侧：JSON 查看器 -->
        <Card class="flex flex-col h-full">
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Database class="h-5 w-5 text-primary" />
              JSON 数据查看器
            </CardTitle>
            <CardDescription> 可视化查看和编辑 JSON 数据，支持语法高亮和树形展示 </CardDescription>
          </CardHeader>
          <CardContent class="flex-1 p-0">
            <div class="h-[600px]">
              <MattEnhancedJsonViewer
                :initial-data="currentJsonData"
                :is-open="true"
                @save="handleJsonSave"
              />
            </div>
          </CardContent>
        </Card>

        <!-- 右侧：使用案例和配置 -->
        <div class="space-y-6">
          <!-- 预设数据案例 -->
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <Settings class="h-5 w-5 text-primary" />
                预设数据案例
              </CardTitle>
              <CardDescription> 点击下方按钮加载不同类型的示例数据 </CardDescription>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <Button
                  v-for="sample in sampleDataList"
                  :key="sample.name"
                  variant="outline"
                  class="justify-start h-auto p-4"
                  @click="loadSpecificSample(sample)"
                >
                  <div class="text-left">
                    <div class="font-medium">{{ sample.name }}</div>
                    <div class="text-xs text-muted-foreground mt-1">
                      {{ sample.description }}
                    </div>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>

          <!-- 数据统计信息 -->
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <BarChart3 class="h-5 w-5 text-primary" />
                数据统计
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div class="space-y-3">
                <div class="flex justify-between items-center">
                  <span class="text-sm text-muted-foreground">数据类型</span>
                  <Badge variant="outline">{{ dataType }}</Badge>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-sm text-muted-foreground">属性数量</span>
                  <span class="text-sm font-medium">{{ propertyCount }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-sm text-muted-foreground">数据大小</span>
                  <span class="text-sm font-medium">{{ dataSize }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-sm text-muted-foreground">嵌套层级</span>
                  <span class="text-sm font-medium">{{ nestingLevel }}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 操作历史 -->
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <History class="h-5 w-5 text-primary" />
                操作历史
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div class="space-y-2 max-h-[200px] overflow-y-auto">
                <div
                  v-for="(action, index) in actionHistory"
                  :key="index"
                  class="flex items-center justify-between p-2 rounded border text-sm"
                >
                  <span>{{ action.description }}</span>
                  <span class="text-xs text-muted-foreground">{{ action.time }}</span>
                </div>
                <div
                  v-if="actionHistory.length === 0"
                  class="text-center text-muted-foreground py-4"
                >
                  暂无操作历史
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { toast } from 'vue-sonner'
import {
  MattEnhancedJsonViewer,
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  Button,
  Badge,
} from '@mattverse/mattverse-ui'
import { FileText, Trash2, Database, Settings, BarChart3, History } from 'lucide-vue-next'

// 当前 JSON 数据
const currentJsonData = ref<any>(null)

// 操作历史记录
const actionHistory = ref<Array<{ description: string; time: string }>>([])

// 示例数据列表
const sampleDataList = ref([
  {
    name: '用户信息',
    description: '包含用户基本信息的 JSON 对象',
    data: {
      id: 1001,
      name: '张三',
      email: '<EMAIL>',
      profile: {
        age: 28,
        city: '北京',
        skills: ['JavaScript', 'Vue.js', 'Node.js'],
        isActive: true,
        lastLogin: '2024-01-15T10:30:00Z',
      },
      preferences: {
        theme: 'dark',
        language: 'zh-CN',
        notifications: {
          email: true,
          push: false,
          sms: true,
        },
      },
    },
  },
  {
    name: '服务器配置',
    description: '服务器配置信息和状态数据',
    data: {
      serverId: 'srv-001',
      name: 'Web服务器',
      type: 'agentServer',
      status: 'running',
      specs: {
        cpu: '8 cores',
        memory: '16GB',
        storage: '500GB SSD',
        network: '1Gbps',
      },
      metrics: {
        cpuUsage: 45.2,
        memoryUsage: 68.7,
        diskUsage: 23.1,
        networkIn: 1024,
        networkOut: 2048,
      },
      services: [
        { name: 'nginx', status: 'active', port: 80 },
        { name: 'nodejs', status: 'active', port: 3000 },
        { name: 'redis', status: 'active', port: 6379 },
      ],
    },
  },
  {
    name: '工作流数据',
    description: '复杂的工作流节点和连接数据',
    data: {
      workflowId: 'wf-12345',
      name: '数据处理流程',
      version: '1.2.0',
      nodes: [
        {
          id: 'node-1',
          type: 'input',
          label: '数据输入',
          position: { x: 100, y: 100 },
          data: { format: 'json', source: 'api' },
        },
        {
          id: 'node-2',
          type: 'transform',
          label: '数据转换',
          position: { x: 300, y: 100 },
          data: { operation: 'filter', conditions: ['status=active'] },
        },
        {
          id: 'node-3',
          type: 'output',
          label: '数据输出',
          position: { x: 500, y: 100 },
          data: { destination: 'database', table: 'processed_data' },
        },
      ],
      edges: [
        { id: 'edge-1', source: 'node-1', target: 'node-2' },
        { id: 'edge-2', source: 'node-2', target: 'node-3' },
      ],
      metadata: {
        created: '2024-01-10T08:00:00Z',
        modified: '2024-01-15T14:30:00Z',
        author: 'admin',
        tags: ['数据处理', '自动化', '生产环境'],
      },
    },
  },
  {
    name: '复杂嵌套数据',
    description: '包含多层嵌套的复杂数据结构',
    data: {
      company: {
        name: 'TechCorp',
        departments: [
          {
            name: '研发部',
            employees: [
              { id: 1, name: '李四', role: '前端工程师' },
              { id: 2, name: '王五', role: '后端工程师' },
            ],
            projects: {
              active: ['项目A', '项目B'],
              completed: ['项目C'],
              budget: { allocated: 1000000, spent: 750000 },
            },
          },
        ],
        locations: {
          headquarters: { city: '北京', address: '朝阳区xxx路' },
          branches: [
            { city: '上海', employees: 50 },
            { city: '深圳', employees: 30 },
          ],
        },
      },
    },
  },
])

// 计算属性
const dataType = computed(() => {
  if (!currentJsonData.value) return '无数据'
  if (Array.isArray(currentJsonData.value)) return '数组'
  if (typeof currentJsonData.value === 'object') return '对象'
  return typeof currentJsonData.value
})

const propertyCount = computed(() => {
  if (!currentJsonData.value) return 0
  if (typeof currentJsonData.value === 'object') {
    return Object.keys(currentJsonData.value).length
  }
  return 0
})

const dataSize = computed(() => {
  if (!currentJsonData.value) return '0 B'
  const jsonString = JSON.stringify(currentJsonData.value)
  const bytes = new Blob([jsonString]).size
  if (bytes < 1024) return `${bytes} B`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
})

const nestingLevel = computed(() => {
  if (!currentJsonData.value) return 0

  const getDepth = (obj: any): number => {
    if (typeof obj !== 'object' || obj === null) return 0
    return 1 + Math.max(0, ...Object.values(obj).map(getDepth))
  }

  return getDepth(currentJsonData.value)
})

// 方法
const loadSampleData = () => {
  // 加载第一个示例数据
  if (sampleDataList.value.length > 0) {
    loadSpecificSample(sampleDataList.value[0])
  }
}

const loadSpecificSample = (sample: any) => {
  currentJsonData.value = sample.data
  addActionHistory(`加载示例数据: ${sample.name}`)
  toast.success(`已加载示例数据: ${sample.name}`)
}

const clearData = () => {
  currentJsonData.value = null
  addActionHistory('清空数据')
  toast.info('已清空数据')
}

const handleJsonSave = (data: any) => {
  currentJsonData.value = data
  addActionHistory('保存 JSON 数据')
  toast.success('JSON 数据已保存')
}

const addActionHistory = (description: string) => {
  const now = new Date()
  const time = now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })

  actionHistory.value.unshift({
    description,
    time,
  })

  // 保持最多 10 条历史记录
  if (actionHistory.value.length > 10) {
    actionHistory.value = actionHistory.value.slice(0, 10)
  }
}

// 初始化时加载示例数据
onMounted(() => {
  loadSampleData()
})
</script>

<style scoped>
/* 确保页面布局正确 */
.page-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
}

.search-section {
  flex-shrink: 0;
}

.content-section {
  flex: 1;
  min-height: 0;
}

/* 响应式优化 */
@media (max-width: 1024px) {
  .content-section .grid {
    grid-template-columns: 1fr;
  }
}
</style>

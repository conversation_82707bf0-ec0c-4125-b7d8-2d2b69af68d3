<template>
  <div class="p-6 space-y-6">
    <div>
      <h1 class="text-2xl font-bold mb-4">PrismJS 测试页面</h1>
      <p class="text-gray-600 mb-6">测试 PrismJS 代码高亮功能</p>
    </div>

    <!-- 测试按钮 -->
    <div class="flex gap-4 mb-6">
      <button 
        @click="loadJsonSample" 
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        加载 JSON 示例
      </button>
      <button 
        @click="loadJsSample" 
        class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
      >
        加载 JavaScript 示例
      </button>
      <button 
        @click="clearCode" 
        class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
      >
        清空
      </button>
    </div>

    <!-- 代码显示区域 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 原始代码 -->
      <div class="border rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-3">原始代码</h3>
        <textarea 
          v-model="rawCode"
          class="w-full h-64 p-3 border rounded font-mono text-sm resize-none"
          placeholder="在这里输入代码..."
        ></textarea>
      </div>

      <!-- 高亮后的代码 -->
      <div class="border rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-3">PrismJS 高亮效果</h3>
        <div class="relative">
          <pre class="language-json"><code 
            class="language-json h-64 overflow-auto block p-3 border rounded text-sm"
            v-html="highlightedCode"
          ></code></pre>
        </div>
      </div>
    </div>

    <!-- 状态信息 -->
    <div class="border rounded-lg p-4 bg-gray-50">
      <h3 class="text-lg font-semibold mb-3">状态信息</h3>
      <div class="space-y-2 text-sm">
        <p><strong>当前语言:</strong> {{ currentLanguage }}</p>
        <p><strong>代码长度:</strong> {{ rawCode.length }} 字符</p>
        <p><strong>PrismJS 状态:</strong> {{ prismStatus }}</p>
        <p><strong>支持的语言:</strong> {{ supportedLanguages.join(', ') }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 动态导入 PrismJS
let Prism: any = null
let prismLoaded = false

const rawCode = ref('')
const currentLanguage = ref('json')
const prismStatus = ref('加载中...')
const supportedLanguages = ref<string[]>([])

// 加载 PrismJS
const loadPrism = async () => {
  try {
    // 动态导入 PrismJS
    const prismModule = await import('prismjs')
    Prism = prismModule.default
    
    // 导入主题
    await import('prismjs/themes/prism-tomorrow.css')
    
    // 导入语言支持
    await import('prismjs/components/prism-json')
    await import('prismjs/components/prism-javascript')
    
    prismLoaded = true
    prismStatus.value = '已加载'
    supportedLanguages.value = Object.keys(Prism.languages)
    
    console.log('PrismJS 加载成功', Prism)
  } catch (error) {
    prismStatus.value = '加载失败: ' + (error as Error).message
    console.error('PrismJS 加载失败:', error)
  }
}

// 计算高亮后的代码
const highlightedCode = computed(() => {
  if (!rawCode.value || !prismLoaded || !Prism) {
    return rawCode.value
  }
  
  try {
    const language = Prism.languages[currentLanguage.value]
    if (language) {
      return Prism.highlight(rawCode.value, language, currentLanguage.value)
    } else {
      return rawCode.value
    }
  } catch (error) {
    console.error('代码高亮失败:', error)
    return rawCode.value
  }
})

// 示例数据
const jsonSample = {
  name: "张三",
  age: 28,
  email: "<EMAIL>",
  address: {
    city: "北京",
    district: "朝阳区"
  },
  hobbies: ["阅读", "游泳", "编程"],
  isActive: true,
  score: 95.5,
  metadata: null
}

const jsSample = `function greetUser(name) {
  const message = \`Hello, \${name}!\`;
  console.log(message);
  return {
    success: true,
    message: message,
    timestamp: new Date().toISOString()
  };
}`

// 方法
const loadJsonSample = () => {
  rawCode.value = JSON.stringify(jsonSample, null, 2)
  currentLanguage.value = 'json'
}

const loadJsSample = () => {
  rawCode.value = jsSample
  currentLanguage.value = 'javascript'
}

const clearCode = () => {
  rawCode.value = ''
}

// 组件挂载时加载 PrismJS
onMounted(() => {
  loadPrism()
  loadJsonSample() // 默认加载 JSON 示例
})
</script>

<style scoped>
/* PrismJS 样式覆盖 */
:deep(.token.property) {
  color: rgb(37 99 235);
}

:deep(.token.string) {
  color: rgb(22 163 74);
}

:deep(.token.number) {
  color: rgb(234 88 12);
}

:deep(.token.boolean) {
  color: rgb(147 51 234);
}

:deep(.token.null) {
  color: rgb(107 114 128);
}

:deep(.token.punctuation) {
  color: rgb(75 85 99);
}

:deep(.token.keyword) {
  color: rgb(147 51 234);
}

:deep(.token.function) {
  color: rgb(37 99 235);
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  :deep(.token.property) {
    color: rgb(96 165 250);
  }

  :deep(.token.string) {
    color: rgb(74 222 128);
  }

  :deep(.token.number) {
    color: rgb(251 146 60);
  }

  :deep(.token.boolean) {
    color: rgb(196 181 253);
  }

  :deep(.token.null) {
    color: rgb(156 163 175);
  }

  :deep(.token.punctuation) {
    color: rgb(209 213 219);
  }

  :deep(.token.keyword) {
    color: rgb(196 181 253);
  }

  :deep(.token.function) {
    color: rgb(96 165 250);
  }
}
</style>
